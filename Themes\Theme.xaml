<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <!-- Color + Brush Tokens - Cyberpunk Inspired -->
  <Color x:Key="BgColor">#0A0A0F</Color>
  <Color x:Key="SurfaceColor">#1A1A2E</Color>
  <Color x:Key="TextColor">#EEEEFF</Color>
  <Color x:Key="MutedTextColor">#9CA3AF</Color>
  <Color x:Key="AccentColor">#00F5FF</Color>
  <Color x:Key="AccentHoverColor">#00E5FF</Color>
  <Color x:Key="FoodColor">#FF6B35</Color>
  <Color x:Key="DangerColor">#FF073A</Color>
  <Color x:Key="NeonGreenColor">#39FF14</Color>
  <Color x:Key="NeonPurpleColor">#BF00FF</Color>
  <Color x:Key="GridColor">#16213E</Color>

  <SolidColorBrush x:Key="BgBrush" Color="{StaticResource BgColor}"/>
  <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
  <SolidColorBrush x:Key="TextBrush" Color="{StaticResource TextColor}"/>
  <SolidColorBrush x:Key="MutedTextBrush" Color="{StaticResource MutedTextColor}"/>
  <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
  <SolidColorBrush x:Key="AccentHoverBrush" Color="{StaticResource AccentHoverColor}"/>
  <SolidColorBrush x:Key="FoodBrush" Color="{StaticResource FoodColor}"/>
  <SolidColorBrush x:Key="DangerBrush" Color="{StaticResource DangerColor}"/>
  <SolidColorBrush x:Key="NeonGreenBrush" Color="{StaticResource NeonGreenColor}"/>
  <SolidColorBrush x:Key="NeonPurpleBrush" Color="{StaticResource NeonPurpleColor}"/>
  <SolidColorBrush x:Key="GridBrush" Color="{StaticResource GridColor}"/>

  <!-- Typography -->
  <Style x:Key="ModernText" TargetType="TextBlock">
    <Setter Property="Foreground" Value="{StaticResource TextBrush}"/>
    <Setter Property="FontFamily" Value="Segoe UI"/>
    <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
    <Setter Property="TextOptions.TextRenderingMode" Value="Auto"/>
  </Style>

  <Style x:Key="ScoreLabel" TargetType="TextBlock" BasedOn="{StaticResource ModernText}">
    <Setter Property="FontSize" Value="24"/>
    <Setter Property="FontWeight" Value="SemiBold"/>
    <Setter Property="FontFamily" Value="Segoe UI"/>
  </Style>

  <!-- Pill Button (modern with glow) -->
  <Style x:Key="PillButton" TargetType="Button">
    <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
    <Setter Property="Foreground" Value="White"/>
    <Setter Property="Padding" Value="18,10"/>
    <Setter Property="FontSize" Value="16"/>
    <Setter Property="FontWeight" Value="SemiBold"/>
    <Setter Property="Cursor" Value="Hand"/>
    <Setter Property="BorderThickness" Value="0"/>
    <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
    <Setter Property="RenderTransform">
      <Setter.Value>
        <ScaleTransform ScaleX="1" ScaleY="1"/>
      </Setter.Value>
    </Setter>
    <Setter Property="Effect">
      <Setter.Value>
        <DropShadowEffect BlurRadius="0" ShadowDepth="0" Color="Transparent"/>
      </Setter.Value>
    </Setter>
    <Setter Property="Template">
      <Setter.Value>
        <ControlTemplate TargetType="Button">
          <Border Background="{TemplateBinding Background}" CornerRadius="12">
            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
          </Border>
        </ControlTemplate>
      </Setter.Value>
    </Setter>
    <Style.Triggers>
      <Trigger Property="IsMouseOver" Value="True">
        <Setter Property="Background" Value="{StaticResource AccentHoverBrush}"/>
        <Trigger.EnterActions>
          <BeginStoryboard Storyboard="{StaticResource ButtonHoverGlow}"/>
        </Trigger.EnterActions>
        <Trigger.ExitActions>
          <BeginStoryboard Storyboard="{StaticResource ButtonHoverGlowReverse}"/>
        </Trigger.ExitActions>
      </Trigger>
      <Trigger Property="IsEnabled" Value="False">
        <Setter Property="Opacity" Value="0.6"/>
      </Trigger>
    </Style.Triggers>
  </Style>

  <!-- Game Field Grid Background (subtle) -->
  <DrawingBrush x:Key="GameGridBrush" ViewportUnits="Absolute" Viewport="0,0,24,24" TileMode="Tile" Stretch="None">
    <DrawingBrush.Drawing>
      <DrawingGroup>
        <!-- Base fill -->
        <GeometryDrawing Brush="#0F1315">
          <GeometryDrawing.Geometry>
            <RectangleGeometry Rect="0,0,24,24"/>
          </GeometryDrawing.Geometry>
        </GeometryDrawing>
        <!-- Grid line (bottom and right) with neon glow -->
        <GeometryDrawing>
          <GeometryDrawing.Pen>
            <Pen Brush="{StaticResource GridBrush}" Thickness="0.5"/>
          </GeometryDrawing.Pen>
          <GeometryDrawing.Geometry>
            <GeometryGroup>
              <LineGeometry StartPoint="0,24" EndPoint="24,24"/>
              <LineGeometry StartPoint="24,0" EndPoint="24,24"/>
            </GeometryGroup>
          </GeometryDrawing.Geometry>
        </GeometryDrawing>
      </DrawingGroup>
    </DrawingBrush.Drawing>
  </DrawingBrush>

  <!-- Animations -->
  <Storyboard x:Key="ScorePopAnimation">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                     To="1.2" Duration="0:0:0.1" AutoReverse="True"/>
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                     To="1.2" Duration="0:0:0.1" AutoReverse="True"/>
  </Storyboard>

  <Storyboard x:Key="FoodPulseAnimation" RepeatBehavior="Forever">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                     From="1.0" To="1.1" Duration="0:0:1.2" AutoReverse="True"/>
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                     From="1.0" To="1.1" Duration="0:0:1.2" AutoReverse="True"/>
  </Storyboard>

  <Storyboard x:Key="GameOverFadeIn">
    <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3"/>
  </Storyboard>

  <Storyboard x:Key="ButtonHoverGlow">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)"
                     To="20" Duration="0:0:0.2"/>
    <ColorAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.Color)"
                    To="#00F5FF" Duration="0:0:0.2"/>
  </Storyboard>

  <Storyboard x:Key="ButtonHoverGlowReverse">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)"
                     To="0" Duration="0:0:0.2"/>
    <ColorAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.Color)"
                    To="Transparent" Duration="0:0:0.2"/>
  </Storyboard>
</ResourceDictionary>

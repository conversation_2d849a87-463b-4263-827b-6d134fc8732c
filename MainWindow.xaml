<Window x:Class="SnakeGame.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Modern Snake Game" 
        Height="700" 
        Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#1a1a1a">
    
    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#45a049"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3d8b40"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Text Style -->
        <Style x:Key="ModernText" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontWeight" Value="Light"/>
        </Style>

        <!-- Score Text Style -->
        <Style x:Key="ScoreText" TargetType="TextBlock" BasedOn="{StaticResource ModernText}">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header Panel -->
        <Border Grid.Row="0" Background="#2d2d2d" CornerRadius="0,0,15,15">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                          Text="SNAKE GAME" 
                          Style="{StaticResource ModernText}"
                          FontSize="28" 
                          FontWeight="Bold"
                          VerticalAlignment="Center"
                          Foreground="#4CAF50"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="Score: " Style="{StaticResource ScoreText}" Margin="0,0,5,0"/>
                    <TextBlock x:Name="ScoreText" Text="0" Style="{StaticResource ScoreText}" Foreground="#4CAF50"/>
                    <TextBlock Text=" | High Score: " Style="{StaticResource ScoreText}" Margin="20,0,5,0"/>
                    <TextBlock x:Name="HighScoreText" Text="0" Style="{StaticResource ScoreText}" Foreground="#FF9800"/>
                </StackPanel>

                <Button Grid.Column="2" 
                       x:Name="PauseButton"
                       Content="PAUSE" 
                       Style="{StaticResource ModernButton}"
                       HorizontalAlignment="Right"
                       Click="PauseButton_Click"/>
            </Grid>
        </Border>

        <!-- Game Area -->
        <Border Grid.Row="1" Background="#0f0f0f" Margin="20" CornerRadius="15">
            <Canvas x:Name="GameCanvas" 
                   Background="#0f0f0f"
                   Focusable="True"
                   KeyDown="GameCanvas_KeyDown"/>
        </Border>

        <!-- Footer Panel -->
        <Border Grid.Row="2" Background="#2d2d2d" CornerRadius="15,15,0,0">
            <Grid Margin="20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                          Text="Use WASD or Arrow Keys to move" 
                          Style="{StaticResource ModernText}"
                          FontSize="14"
                          VerticalAlignment="Center"
                          Opacity="0.7"/>

                <Button Grid.Column="1" 
                       x:Name="NewGameButton"
                       Content="NEW GAME" 
                       Style="{StaticResource ModernButton}"
                       Click="NewGameButton_Click"/>

                <TextBlock Grid.Column="2" 
                          Text="Press SPACE to pause" 
                          Style="{StaticResource ModernText}"
                          FontSize="14"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Right"
                          Opacity="0.7"/>
            </Grid>
        </Border>

        <!-- Game Over Overlay -->
        <Border x:Name="GameOverOverlay" 
               Grid.RowSpan="3"
               Background="#CC000000"
               Visibility="Collapsed">
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="GAME OVER" 
                          Style="{StaticResource ModernText}"
                          FontSize="48" 
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Foreground="#FF5722"
                          Margin="0,0,0,20"/>
                
                <TextBlock x:Name="FinalScoreText" 
                          Text="Final Score: 0" 
                          Style="{StaticResource ModernText}"
                          FontSize="24"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,30"/>
                
                <Button Content="PLAY AGAIN" 
                       Style="{StaticResource ModernButton}"
                       Click="NewGameButton_Click"
                       Padding="30,15"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>

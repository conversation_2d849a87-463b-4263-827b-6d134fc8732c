<Window x:Class="SnakeGame.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Snake — 2024 Edition"
        Height="720"
        Width="1024"
        MinHeight="600"
        MinWidth="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="{DynamicResource BgBrush}">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Themes/Theme.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>


    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header Panel -->
        <Border Grid.Row="0" Background="{DynamicResource SurfaceBrush}" CornerRadius="12" Margin="16,16,16,8" Padding="16">
            <Border.Effect>
                <DropShadowEffect BlurRadius="18" ShadowDepth="0" Opacity="0.35"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="◢ CYBER SNAKE ◣"
                           Style="{StaticResource ModernText}"
                           FontSize="24"
                           FontWeight="Bold"
                           VerticalAlignment="Center"
                           Foreground="{DynamicResource AccentBrush}">
                    <TextBlock.Effect>
                        <DropShadowEffect BlurRadius="10" ShadowDepth="0" Color="#00F5FF" Opacity="0.6"/>
                    </TextBlock.Effect>
                </TextBlock>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,0,0">
                    <TextBlock Text="Score" Style="{StaticResource ModernText}" Foreground="{DynamicResource MutedTextBrush}"/>
                    <TextBlock x:Name="ScoreText" Text="0" Style="{StaticResource ScoreLabel}">
                        <TextBlock.RenderTransform>
                            <ScaleTransform ScaleX="1" ScaleY="1"/>
                        </TextBlock.RenderTransform>
                    </TextBlock>
                    <TextBlock Text="·" Margin="8,0" Style="{StaticResource ModernText}" Foreground="{DynamicResource MutedTextBrush}"/>
                    <TextBlock Text="High" Style="{StaticResource ModernText}" Foreground="{DynamicResource MutedTextBrush}"/>
                    <TextBlock x:Name="HighScoreText" Text="0" Style="{StaticResource ScoreLabel}" Foreground="{DynamicResource FoodBrush}"/>
                </StackPanel>

                <Button Grid.Column="2"
                        x:Name="PauseButton"
                        Content="Pause"
                        Style="{StaticResource PillButton}"
                        HorizontalAlignment="Right"
                        Click="PauseButton_Click"/>
            </Grid>
        </Border>

        <!-- Game Area -->
        <Border Grid.Row="1" Background="{DynamicResource SurfaceBrush}" Margin="16,8,16,8" CornerRadius="16" Padding="8">
            <Border.Effect>
                <DropShadowEffect BlurRadius="24" ShadowDepth="0" Opacity="0.35"/>
            </Border.Effect>
            <Border CornerRadius="12">
                <Border.Background>
                    <StaticResource ResourceKey="GameGridBrush"/>
                </Border.Background>
                <Border.Effect>
                    <DropShadowEffect BlurRadius="18" ShadowDepth="0" Color="#162A1E" Opacity="0.35"/>
                </Border.Effect>
                <Canvas x:Name="GameCanvas"
                        SnapsToDevicePixels="True"
                        Background="{DynamicResource BgBrush}"
                        Focusable="True"
                        KeyDown="GameCanvas_KeyDown"/>
            </Border>
        </Border>

        <!-- Footer Panel -->
        <Border Grid.Row="2" Background="{DynamicResource SurfaceBrush}" CornerRadius="12" Margin="16,8,16,16" Padding="12">
            <Border.Effect>
                <DropShadowEffect BlurRadius="16" ShadowDepth="0" Opacity="0.25"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="WASD/Arrows to move"
                          Style="{StaticResource ModernText}"
                          FontSize="14"
                          VerticalAlignment="Center"
                          Foreground="{DynamicResource MutedTextBrush}"/>

                <Button Grid.Column="1"
                        x:Name="NewGameButton"
                        Content="New Game"
                        Style="{StaticResource PillButton}"
                        Click="NewGameButton_Click"/>

                <TextBlock Grid.Column="2"
                          Text="Space to pause"
                          Style="{StaticResource ModernText}"
                          FontSize="14"
                          VerticalAlignment="Center"
                          HorizontalAlignment="Right"
                          Foreground="{DynamicResource MutedTextBrush}"/>
            </Grid>
        </Border>

        <!-- Game Over Overlay -->
        <Border x:Name="GameOverOverlay"
               Grid.RowSpan="3"
               Background="#DD000000"
               Visibility="Collapsed"
               Opacity="0">
            <Border.Effect>
                <BlurEffect Radius="2"/>
            </Border.Effect>
            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                <TextBlock Text="◢ SYSTEM FAILURE ◣"
                          Style="{StaticResource ModernText}"
                          FontSize="48"
                          FontWeight="Bold"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource DangerBrush}"
                          Margin="0,0,0,20">
                    <TextBlock.Effect>
                        <DropShadowEffect BlurRadius="15" ShadowDepth="0" Color="#FF073A" Opacity="0.8"/>
                    </TextBlock.Effect>
                </TextBlock>

                <TextBlock x:Name="FinalScoreText"
                          Text="Final Score: 0"
                          Style="{StaticResource ModernText}"
                          FontSize="24"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,30"
                          Foreground="{DynamicResource AccentBrush}"/>

                <Button Content="◢ RESTART SYSTEM ◣"
                       Style="{StaticResource PillButton}"
                       Click="NewGameButton_Click"
                       Padding="30,15"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace SnakeGame
{
    public partial class MainWindow : Window
    {
        private const int GridSize = 20;
        private const int GameAreaWidth = 40;
        private const int GameAreaHeight = 30;
        
        private DispatcherTimer gameTimer;
        private List<Point> snake;
        private Point food;
        private Direction currentDirection;
        private Direction nextDirection;
        private Random random;
        private int score;
        private int highScore;
        private bool isGameRunning;
        private bool isPaused;

        public MainWindow()
        {
            InitializeComponent();
            InitializeGame();
            GameCanvas.Focus();
        }

        private void InitializeGame()
        {
            gameTimer = new DispatcherTimer();
            gameTimer.Tick += GameTimer_Tick;
            gameTimer.Interval = TimeSpan.FromMilliseconds(150);
            
            snake = new List<Point>();
            random = new Random();
            
            LoadHighScore();
            StartNewGame();
        }

        private void StartNewGame()
        {
            snake.Clear();
            GameCanvas.Children.Clear();
            
            // Initialize snake in the center
            snake.Add(new Point(GameAreaWidth / 2, GameAreaHeight / 2));
            snake.Add(new Point(GameAreaWidth / 2 - 1, GameAreaHeight / 2));
            snake.Add(new Point(GameAreaWidth / 2 - 2, GameAreaHeight / 2));
            
            currentDirection = Direction.Right;
            nextDirection = Direction.Right;
            score = 0;
            isGameRunning = true;
            isPaused = false;
            
            UpdateScore();
            GenerateFood();
            DrawGame();
            
            GameOverOverlay.Visibility = Visibility.Collapsed;
            PauseButton.Content = "PAUSE";
            gameTimer.Start();
        }

        private void GameTimer_Tick(object sender, EventArgs e)
        {
            if (!isGameRunning || isPaused) return;
            
            currentDirection = nextDirection;
            MoveSnake();
            
            if (CheckCollision())
            {
                GameOver();
                return;
            }
            
            if (CheckFoodCollision())
            {
                score += 10;
                UpdateScore();
                GenerateFood();
                
                // Increase speed slightly as score increases
                if (score % 50 == 0 && gameTimer.Interval.TotalMilliseconds > 80)
                {
                    gameTimer.Interval = TimeSpan.FromMilliseconds(gameTimer.Interval.TotalMilliseconds - 10);
                }
            }
            else
            {
                snake.RemoveAt(snake.Count - 1);
            }
            
            DrawGame();
        }

        private void MoveSnake()
        {
            Point head = snake[0];
            Point newHead = head;
            
            switch (currentDirection)
            {
                case Direction.Up:
                    newHead.Y--;
                    break;
                case Direction.Down:
                    newHead.Y++;
                    break;
                case Direction.Left:
                    newHead.X--;
                    break;
                case Direction.Right:
                    newHead.X++;
                    break;
            }
            
            snake.Insert(0, newHead);
        }

        private bool CheckCollision()
        {
            Point head = snake[0];
            
            // Check wall collision
            if (head.X < 0 || head.X >= GameAreaWidth || head.Y < 0 || head.Y >= GameAreaHeight)
                return true;
            
            // Check self collision
            for (int i = 1; i < snake.Count; i++)
            {
                if (snake[i].X == head.X && snake[i].Y == head.Y)
                    return true;
            }
            
            return false;
        }

        private bool CheckFoodCollision()
        {
            return snake[0].X == food.X && snake[0].Y == food.Y;
        }

        private void GenerateFood()
        {
            do
            {
                food = new Point(random.Next(0, GameAreaWidth), random.Next(0, GameAreaHeight));
            }
            while (snake.Any(segment => segment.X == food.X && segment.Y == food.Y));
        }

        private void DrawGame()
        {
            GameCanvas.Children.Clear();
            
            // Draw snake
            for (int i = 0; i < snake.Count; i++)
            {
                Rectangle snakeSegment = new Rectangle
                {
                    Width = GridSize - 2,
                    Height = GridSize - 2,
                    Fill = i == 0 ? new SolidColorBrush(Color.FromRgb(76, 175, 80)) : // Head - brighter green
                                   new SolidColorBrush(Color.FromRgb(56, 142, 60)),    // Body - darker green
                    RadiusX = 3,
                    RadiusY = 3
                };
                
                Canvas.SetLeft(snakeSegment, snake[i].X * GridSize + 1);
                Canvas.SetTop(snakeSegment, snake[i].Y * GridSize + 1);
                GameCanvas.Children.Add(snakeSegment);
            }
            
            // Draw food
            Ellipse foodEllipse = new Ellipse
            {
                Width = GridSize - 4,
                Height = GridSize - 4,
                Fill = new SolidColorBrush(Color.FromRgb(255, 152, 0)) // Orange
            };
            
            Canvas.SetLeft(foodEllipse, food.X * GridSize + 2);
            Canvas.SetTop(foodEllipse, food.Y * GridSize + 2);
            GameCanvas.Children.Add(foodEllipse);
        }

        private void GameOver()
        {
            isGameRunning = false;
            gameTimer.Stop();
            
            if (score > highScore)
            {
                highScore = score;
                SaveHighScore();
                UpdateHighScore();
            }
            
            FinalScoreText.Text = $"Final Score: {score}";
            GameOverOverlay.Visibility = Visibility.Visible;
        }

        private void UpdateScore()
        {
            ScoreText.Text = score.ToString();
        }

        private void UpdateHighScore()
        {
            HighScoreText.Text = highScore.ToString();
        }

        private void LoadHighScore()
        {
            try
            {
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string filePath = System.IO.Path.Combine(appDataPath, "SnakeGame", "highscore.txt");
                
                if (System.IO.File.Exists(filePath))
                {
                    string content = System.IO.File.ReadAllText(filePath);
                    if (int.TryParse(content, out int savedScore))
                    {
                        highScore = savedScore;
                    }
                }
            }
            catch
            {
                highScore = 0;
            }
            
            UpdateHighScore();
        }

        private void SaveHighScore()
        {
            try
            {
                string appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                string folderPath = System.IO.Path.Combine(appDataPath, "SnakeGame");
                string filePath = System.IO.Path.Combine(folderPath, "highscore.txt");
                
                System.IO.Directory.CreateDirectory(folderPath);
                System.IO.File.WriteAllText(filePath, highScore.ToString());
            }
            catch
            {
                // Ignore save errors
            }
        }

        private void GameCanvas_KeyDown(object sender, KeyEventArgs e)
        {
            if (!isGameRunning) return;
            
            Direction newDirection = currentDirection;
            
            switch (e.Key)
            {
                case Key.W:
                case Key.Up:
                    if (currentDirection != Direction.Down)
                        newDirection = Direction.Up;
                    break;
                case Key.S:
                case Key.Down:
                    if (currentDirection != Direction.Up)
                        newDirection = Direction.Down;
                    break;
                case Key.A:
                case Key.Left:
                    if (currentDirection != Direction.Right)
                        newDirection = Direction.Left;
                    break;
                case Key.D:
                case Key.Right:
                    if (currentDirection != Direction.Left)
                        newDirection = Direction.Right;
                    break;
                case Key.Space:
                    TogglePause();
                    break;
            }
            
            nextDirection = newDirection;
        }

        private void PauseButton_Click(object sender, RoutedEventArgs e)
        {
            TogglePause();
        }

        private void TogglePause()
        {
            if (!isGameRunning) return;
            
            isPaused = !isPaused;
            PauseButton.Content = isPaused ? "RESUME" : "PAUSE";
        }

        private void NewGameButton_Click(object sender, RoutedEventArgs e)
        {
            StartNewGame();
            GameCanvas.Focus();
        }
    }

    public enum Direction
    {
        Up,
        Down,
        Left,
        Right
    }
}
